# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لنظام نقطة البيع
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QMenu, QAction, QStatusBar, QLabel,
                            QFrame, QPushButton, QGridLayout, QMessageBox,
                            QApplication, QSplashScreen, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QIcon, QPixmap
from datetime import datetime

from database.database_manager import DatabaseManager
from utils.styles import NEUMORPHIC_STYLES, apply_rtl_layout, set_arabic_font
from ui.login_window import LoginWindow
from ui.sales_window import SalesWindow
from ui.inventory_window import InventoryWindow
from ui.reports_window import ReportsWindow
from config.settings import STORE_CONFIG, UI_CONFIG

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_user = None
        self.sales_window = None
        self.inventory_window = None
        self.reports_window = None
        
        # إعداد النافذة
        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_styles()
        
        # عرض نافذة تسجيل الدخول
        self.show_login()
        
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # كل ثانية
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"{STORE_CONFIG['name']} - نظام نقطة البيع")
        self.setGeometry(100, 100, 1200, 800)
        
        # تطبيق تخطيط RTL
        apply_rtl_layout(self)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إطار الترحيب
        welcome_frame = self.create_welcome_frame()
        main_layout.addWidget(welcome_frame)
        
        # إطار الأزرار الرئيسية
        buttons_frame = self.create_main_buttons_frame()
        main_layout.addWidget(buttons_frame)
        
        # إطار الإحصائيات السريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # إطار التنبيهات
        alerts_frame = self.create_alerts_frame()
        main_layout.addWidget(alerts_frame)
    
    def create_welcome_frame(self):
        """إنشاء إطار الترحيب"""
        frame = QFrame()
        frame.setObjectName("welcome_frame")
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(30, 20, 30, 20)
        
        # معلومات المتجر
        store_info_layout = QVBoxLayout()
        
        store_name = QLabel(STORE_CONFIG['name'])
        store_name.setObjectName("store_name")
        set_arabic_font(store_name, 24, True)
        store_info_layout.addWidget(store_name)
        
        store_address = QLabel(STORE_CONFIG['address'])
        store_address.setObjectName("store_address")
        set_arabic_font(store_address, 12)
        store_info_layout.addWidget(store_address)
        
        layout.addLayout(store_info_layout)
        layout.addStretch()
        
        # معلومات المستخدم والوقت
        user_info_layout = QVBoxLayout()
        user_info_layout.setAlignment(Qt.AlignRight)
        
        self.user_label = QLabel("مرحباً")
        self.user_label.setObjectName("user_label")
        set_arabic_font(self.user_label, 14, True)
        user_info_layout.addWidget(self.user_label)
        
        self.time_label = QLabel()
        self.time_label.setObjectName("time_label")
        set_arabic_font(self.time_label, 12)
        user_info_layout.addWidget(self.time_label)
        
        layout.addLayout(user_info_layout)
        
        return frame
    
    def create_main_buttons_frame(self):
        """إنشاء إطار الأزرار الرئيسية"""
        frame = QFrame()
        frame.setObjectName("buttons_frame")
        
        layout = QGridLayout(frame)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # أزرار النظام الرئيسية
        buttons = [
            ("💰", "المبيعات", "بدء عملية بيع جديدة", self.open_sales),
            ("📦", "المخزون", "إدارة المنتجات والمخزون", self.open_inventory),
            ("📊", "التقارير", "عرض تقارير المبيعات والأرباح", self.open_reports),
            ("👥", "المستخدمين", "إدارة المستخدمين والصلاحيات", self.open_users),
            ("⚙️", "الإعدادات", "إعدادات النظام والمتجر", self.open_settings),
            ("💾", "النسخ الاحتياطي", "إنشاء نسخة احتياطية", self.create_backup)
        ]
        
        for i, (icon, title, description, callback) in enumerate(buttons):
            button = self.create_main_button(icon, title, description, callback)
            row = i // 3
            col = i % 3
            layout.addWidget(button, row, col)
        
        return frame
    
    def create_main_button(self, icon, title, description, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton()
        button.setObjectName("main_button")
        button.clicked.connect(callback)
        
        # تخطيط الزر
        button_layout = QVBoxLayout()
        button_layout.setAlignment(Qt.AlignCenter)
        button_layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin-bottom: 5px;")
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("button_title")
        set_arabic_font(title_label, 14, True)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("button_description")
        desc_label.setWordWrap(True)
        set_arabic_font(desc_label, 10)
        
        # إضافة العناصر إلى ويدجت
        widget = QWidget()
        widget_layout = QVBoxLayout(widget)
        widget_layout.addWidget(icon_label)
        widget_layout.addWidget(title_label)
        widget_layout.addWidget(desc_label)
        
        button.setMinimumSize(180, 120)
        button.setMaximumSize(200, 140)
        
        return button
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات السريعة"""
        frame = QFrame()
        frame.setObjectName("stats_frame")
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(30, 20, 30, 20)
        layout.setSpacing(20)
        
        # إحصائيات اليوم
        today_stats = self.get_today_stats()
        
        stats = [
            ("💰", "مبيعات اليوم", f"{today_stats['sales']:.2f} دج"),
            ("🧾", "عدد الفواتير", str(today_stats['invoices'])),
            ("📦", "المنتجات", str(today_stats['products'])),
            ("⚠️", "تنبيهات المخزون", str(today_stats['low_stock']))
        ]
        
        for icon, title, value in stats:
            stat_widget = self.create_stat_widget(icon, title, value)
            layout.addWidget(stat_widget)
        
        return frame
    
    def create_stat_widget(self, icon, title, value):
        """إنشاء ويدجت إحصائية"""
        widget = QFrame()
        widget.setObjectName("stat_widget")
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px;")
        layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("stat_value")
        set_arabic_font(value_label, 16, True)
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("stat_title")
        set_arabic_font(title_label, 11)
        layout.addWidget(title_label)
        
        widget.setMinimumSize(150, 100)
        return widget
    
    def create_alerts_frame(self):
        """إنشاء إطار التنبيهات"""
        frame = QFrame()
        frame.setObjectName("alerts_frame")
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # عنوان التنبيهات
        alerts_title = QLabel("🔔 التنبيهات")
        alerts_title.setObjectName("alerts_title")
        set_arabic_font(alerts_title, 14, True)
        layout.addWidget(alerts_title)
        
        # قائمة التنبيهات
        self.alerts_container = QVBoxLayout()
        layout.addLayout(self.alerts_container)
        
        # تحديث التنبيهات
        self.update_alerts()
        
        return frame
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        menubar.setObjectName("menu_bar")
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_sale_action = QAction("مبيعة جديدة", self)
        new_sale_action.setShortcut("Ctrl+N")
        new_sale_action.triggered.connect(self.open_sales)
        file_menu.addAction(new_sale_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        logout_action = QAction("تسجيل خروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        sales_action = QAction("المبيعات", self)
        sales_action.triggered.connect(self.open_sales)
        view_menu.addAction(sales_action)
        
        inventory_action = QAction("المخزون", self)
        inventory_action.triggered.connect(self.open_inventory)
        view_menu.addAction(inventory_action)
        
        reports_action = QAction("التقارير", self)
        reports_action.triggered.connect(self.open_reports)
        view_menu.addAction(reports_action)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")
        
        users_action = QAction("إدارة المستخدمين", self)
        users_action.triggered.connect(self.open_users)
        tools_menu.addAction(users_action)
        
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        tools_menu.addAction(settings_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.setObjectName("status_bar")
        
        # معلومات الاتصال بقاعدة البيانات
        self.db_status_label = QLabel("متصل بقاعدة البيانات")
        self.status_bar.addWidget(self.db_status_label)
        
        self.status_bar.addPermanentWidget(QLabel("|"))
        
        # معلومات المستخدم الحالي
        self.current_user_label = QLabel()
        self.status_bar.addPermanentWidget(self.current_user_label)
    
    def setup_styles(self):
        """إعداد الأنماط"""
        style = f"""
            QMainWindow {{
                background-color: #E0E5EC;
                color: #2C3E50;
            }}
            
            #welcome_frame {{
                background-color: #E0E5EC;
                border-radius: 20px;
                padding: 20px;
                box-shadow: 10px 10px 20px #A3B1C6, 
                           -10px -10px 20px #FFFFFF;
            }}
            
            #store_name {{
                color: #2C3E50;
                font-weight: bold;
            }}
            
            #store_address {{
                color: #6C757D;
            }}
            
            #user_label {{
                color: #4A90E2;
                font-weight: bold;
            }}
            
            #time_label {{
                color: #6C757D;
            }}
            
            #buttons_frame {{
                background-color: #E0E5EC;
                border-radius: 20px;
                box-shadow: 10px 10px 20px #A3B1C6, 
                           -10px -10px 20px #FFFFFF;
            }}
            
            #main_button {{
                background-color: #E0E5EC;
                border: none;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 8px 8px 16px #A3B1C6, 
                           -8px -8px 16px #FFFFFF;
            }}
            
            #main_button:hover {{
                background-color: #4A90E2;
                color: white;
                box-shadow: inset 4px 4px 8px rgba(0,0,0,0.2), 
                           inset -4px -4px 8px rgba(255,255,255,0.1);
            }}
            
            #main_button:pressed {{
                box-shadow: inset 6px 6px 12px rgba(0,0,0,0.3), 
                           inset -6px -6px 12px rgba(255,255,255,0.1);
            }}
            
            #button_title {{
                color: #2C3E50;
                font-weight: bold;
            }}
            
            #button_description {{
                color: #6C757D;
            }}
            
            #stats_frame {{
                background-color: #E0E5EC;
                border-radius: 20px;
                box-shadow: 10px 10px 20px #A3B1C6, 
                           -10px -10px 20px #FFFFFF;
            }}
            
            #stat_widget {{
                background-color: #E0E5EC;
                border-radius: 12px;
                padding: 15px;
                box-shadow: 6px 6px 12px #A3B1C6, 
                           -6px -6px 12px #FFFFFF;
            }}
            
            #stat_value {{
                color: #4A90E2;
                font-weight: bold;
            }}
            
            #stat_title {{
                color: #6C757D;
            }}
            
            #alerts_frame {{
                background-color: #E0E5EC;
                border-radius: 15px;
                box-shadow: 8px 8px 16px #A3B1C6, 
                           -8px -8px 16px #FFFFFF;
            }}
            
            #alerts_title {{
                color: #2C3E50;
                font-weight: bold;
                margin-bottom: 10px;
            }}
            
            QMenuBar {{
                background-color: #E0E5EC;
                color: #2C3E50;
                border: none;
                padding: 5px;
                box-shadow: 0px 2px 4px #A3B1C6;
            }}
            
            QMenuBar::item {{
                background-color: transparent;
                padding: 8px 16px;
                border-radius: 8px;
            }}
            
            QMenuBar::item:selected {{
                background-color: #4A90E2;
                color: white;
            }}
            
            QMenu {{
                background-color: #E0E5EC;
                border: none;
                border-radius: 12px;
                padding: 8px;
                box-shadow: 8px 8px 16px #A3B1C6;
            }}
            
            QMenu::item {{
                padding: 8px 16px;
                border-radius: 6px;
            }}
            
            QMenu::item:selected {{
                background-color: #4A90E2;
                color: white;
            }}
            
            QStatusBar {{
                background-color: #E0E5EC;
                color: #2C3E50;
                border: none;
                padding: 5px;
                box-shadow: 0px -2px 4px #A3B1C6;
            }}
        """
        
        self.setStyleSheet(style)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        self.login_window.show()
        self.hide()
    
    def on_login_successful(self, user):
        """عند نجاح تسجيل الدخول"""
        self.current_user = user
        self.user_label.setText(f"مرحباً، {user['full_name']}")
        self.current_user_label.setText(f"المستخدم: {user['username']} ({user['role']})")
        
        # تحديث الواجهة حسب صلاحيات المستخدم
        self.update_ui_permissions()
        
        self.show()
        self.update_alerts()
    
    def update_ui_permissions(self):
        """تحديث الواجهة حسب صلاحيات المستخدم"""
        # يمكن إضافة منطق إخفاء/إظهار الأزرار حسب الصلاحيات
        pass
    
    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now()
        time_str = current_time.strftime("%Y/%m/%d - %H:%M:%S")
        self.time_label.setText(time_str)
    
    def get_today_stats(self):
        """الحصول على إحصائيات اليوم"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        # مبيعات اليوم
        sales_report = self.db_manager.get_sales_report(today, today)
        total_sales = sum(sale['total_amount'] for sale in sales_report)
        
        # عدد الفواتير
        invoices_count = len(sales_report)
        
        # عدد المنتجات
        products = self.db_manager.get_all_products()
        products_count = len(products)
        
        # تنبيهات المخزون
        low_stock = self.db_manager.get_low_stock_products()
        low_stock_count = len(low_stock)
        
        return {
            'sales': total_sales,
            'invoices': invoices_count,
            'products': products_count,
            'low_stock': low_stock_count
        }
    
    def update_alerts(self):
        """تحديث التنبيهات"""
        # مسح التنبيهات الحالية
        for i in reversed(range(self.alerts_container.count())):
            self.alerts_container.itemAt(i).widget().setParent(None)
        
        # تنبيهات المخزون المنخفض
        low_stock_products = self.db_manager.get_low_stock_products()
        
        if low_stock_products:
            for product in low_stock_products[:5]:  # أول 5 منتجات
                alert_text = f"⚠️ {product['name']} - الكمية: {product['stock_quantity']}"
                alert_label = QLabel(alert_text)
                alert_label.setObjectName("alert_label")
                alert_label.setStyleSheet("""
                    #alert_label {
                        background-color: rgba(255, 193, 7, 0.1);
                        border: 1px solid rgba(255, 193, 7, 0.3);
                        border-radius: 8px;
                        padding: 8px;
                        margin: 2px;
                        color: #856404;
                    }
                """)
                set_arabic_font(alert_label, 11)
                self.alerts_container.addWidget(alert_label)
        else:
            no_alerts = QLabel("✅ لا توجد تنبيهات")
            no_alerts.setStyleSheet("""
                background-color: rgba(40, 167, 69, 0.1);
                border: 1px solid rgba(40, 167, 69, 0.3);
                border-radius: 8px;
                padding: 8px;
                color: #155724;
            """)
            set_arabic_font(no_alerts, 11)
            self.alerts_container.addWidget(no_alerts)
    
    # دوال فتح النوافذ
    def open_sales(self):
        """فتح نافذة المبيعات"""
        if not self.sales_window:
            from ui.sales_window import SalesWindow
            self.sales_window = SalesWindow(self.current_user, self.db_manager)
        
        self.sales_window.show()
        self.sales_window.raise_()
        self.sales_window.activateWindow()
    
    def open_inventory(self):
        """فتح نافذة المخزون"""
        if not self.inventory_window:
            from ui.inventory_window import InventoryWindow
            self.inventory_window = InventoryWindow(self.current_user, self.db_manager)
        
        self.inventory_window.show()
        self.inventory_window.raise_()
        self.inventory_window.activateWindow()
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        if not self.reports_window:
            from ui.reports_window import ReportsWindow
            self.reports_window = ReportsWindow(self.current_user, self.db_manager)
        
        self.reports_window.show()
        self.reports_window.raise_()
        self.reports_window.activateWindow()
    
    def open_users(self):
        """فتح نافذة إدارة المستخدمين"""
        QMessageBox.information(self, "قريباً", "نافذة إدارة المستخدمين قيد التطوير")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        QMessageBox.information(self, "قريباً", "نافذة الإعدادات قيد التطوير")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            success = self.db_manager.backup_database()
            if success:
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح")
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تسجيل خروج", 
                                   "هل تريد تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.current_user = None
            self.hide()
            self.show_login()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
        <div style='text-align: center; direction: rtl;'>
            <h2>{STORE_CONFIG['name']}</h2>
            <h3>نظام نقطة البيع</h3>
            <p><b>الإصدار:</b> 1.0</p>
            <p><b>تطوير:</b> فريق التطوير</p>
            <p><b>التاريخ:</b> 2024</p>
            <br>
            <p>نظام شامل لإدارة المبيعات والمخزون</p>
            <p>مع دعم كامل للغة العربية</p>
        </div>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(self, "إغلاق البرنامج",
                                   "هل تريد إغلاق البرنامج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # إغلاق جميع النوافذ الفرعية
            if self.sales_window:
                self.sales_window.close()
            if self.inventory_window:
                self.inventory_window.close()
            if self.reports_window:
                self.reports_window.close()
            
            event.accept()
        else:
            event.ignore()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تعيين خط عربي للتطبيق
    font = QFont("Arial", 12)
    app.setFont(font)
    
    # تعيين اتجاه RTL
    app.setLayoutDirection(Qt.RightToLeft)
    
    main_window = MainWindow()
    
    sys.exit(app.exec_())
