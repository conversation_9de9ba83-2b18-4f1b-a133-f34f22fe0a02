# -*- coding: utf-8 -*-
"""
نموذج المبيعات
"""

from dataclasses import dataclass, field
from typing import List, Optional
from datetime import datetime
from .product import Product

@dataclass
class SaleItem:
    """عنصر في الفاتورة"""
    product_id: int
    product_name: str
    barcode: str
    quantity: int
    unit_price: float
    total_price: float
    discount_amount: float = 0.0
    unit: str = "قطعة"
    
    def __post_init__(self):
        """التحقق من صحة البيانات"""
        if self.quantity <= 0:
            raise ValueError("الكمية يجب أن تكون أكبر من صفر")
        if self.unit_price < 0:
            raise ValueError("السعر يجب أن يكون أكبر من أو يساوي صفر")
    
    @property
    def net_price(self) -> float:
        """السعر الصافي بعد الخصم"""
        return self.total_price - self.discount_amount
    
    @property
    def discount_percentage(self) -> float:
        """نسبة الخصم"""
        if self.total_price > 0:
            return (self.discount_amount / self.total_price) * 100
        return 0.0
    
    def apply_discount(self, discount_percentage: float):
        """تطبيق خصم بالنسبة المئوية"""
        self.discount_amount = self.total_price * (discount_percentage / 100)
    
    def to_dict(self) -> dict:
        """تحويل إلى قاموس"""
        return {
            'product_id': self.product_id,
            'product_name': self.product_name,
            'barcode': self.barcode,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_price': self.total_price,
            'discount_amount': self.discount_amount,
            'unit': self.unit
        }

@dataclass
class Sale:
    """نموذج بيانات المبيعات"""
    id: Optional[int] = None
    receipt_number: str = ""
    user_id: int = 0
    cashier_name: str = ""
    customer_name: str = ""
    customer_phone: str = ""
    items: List[SaleItem] = field(default_factory=list)
    subtotal: float = 0.0
    discount_amount: float = 0.0
    discount_percentage: float = 0.0
    tax_amount: float = 0.0
    tax_rate: float = 19.0  # الضريبة في الجزائر
    total_amount: float = 0.0
    paid_amount: float = 0.0
    change_amount: float = 0.0
    payment_method: str = "نقدي"
    status: str = "مكتملة"
    notes: str = ""
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """حساب المبالغ تلقائياً"""
        if self.items:
            self.calculate_totals()
    
    def add_item(self, product: Product, quantity: int, discount_percentage: float = 0):
        """إضافة منتج إلى الفاتورة"""
        # التحقق من وجود المنتج مسبقاً
        existing_item = self.find_item_by_product_id(product.id)
        
        if existing_item:
            # زيادة الكمية للمنتج الموجود
            existing_item.quantity += quantity
            existing_item.total_price = existing_item.quantity * existing_item.unit_price
            if discount_percentage > 0:
                existing_item.apply_discount(discount_percentage)
        else:
            # إضافة منتج جديد
            total_price = product.price * quantity
            discount_amount = total_price * (discount_percentage / 100)
            
            item = SaleItem(
                product_id=product.id,
                product_name=product.name,
                barcode=product.barcode,
                quantity=quantity,
                unit_price=product.price,
                total_price=total_price,
                discount_amount=discount_amount,
                unit=product.unit
            )
            self.items.append(item)
        
        self.calculate_totals()
    
    def remove_item(self, product_id: int):
        """حذف منتج من الفاتورة"""
        self.items = [item for item in self.items if item.product_id != product_id]
        self.calculate_totals()
    
    def update_item_quantity(self, product_id: int, new_quantity: int):
        """تحديث كمية منتج"""
        item = self.find_item_by_product_id(product_id)
        if item:
            if new_quantity <= 0:
                self.remove_item(product_id)
            else:
                item.quantity = new_quantity
                item.total_price = item.quantity * item.unit_price
                # إعادة تطبيق الخصم
                if item.discount_amount > 0:
                    discount_percentage = item.discount_percentage
                    item.apply_discount(discount_percentage)
                self.calculate_totals()
    
    def find_item_by_product_id(self, product_id: int) -> Optional[SaleItem]:
        """البحث عن منتج في الفاتورة"""
        for item in self.items:
            if item.product_id == product_id:
                return item
        return None
    
    def calculate_totals(self):
        """حساب المجاميع"""
        # حساب المجموع الفرعي
        self.subtotal = sum(item.total_price for item in self.items)
        
        # حساب إجمالي الخصومات
        total_item_discounts = sum(item.discount_amount for item in self.items)
        
        # حساب المجموع بعد خصم العناصر
        subtotal_after_item_discounts = self.subtotal - total_item_discounts
        
        # تطبيق خصم إضافي على الفاتورة
        additional_discount = subtotal_after_item_discounts * (self.discount_percentage / 100)
        self.discount_amount = total_item_discounts + additional_discount
        
        # حساب المجموع بعد جميع الخصومات
        amount_after_discounts = self.subtotal - self.discount_amount
        
        # حساب الضريبة
        self.tax_amount = amount_after_discounts * (self.tax_rate / 100)
        
        # حساب المجموع النهائي
        self.total_amount = amount_after_discounts + self.tax_amount
        
        # حساب الباقي
        self.change_amount = max(0, self.paid_amount - self.total_amount)
    
    def apply_discount(self, discount_percentage: float):
        """تطبيق خصم على الفاتورة كاملة"""
        self.discount_percentage = discount_percentage
        self.calculate_totals()
    
    def set_payment(self, paid_amount: float, payment_method: str = "نقدي"):
        """تسجيل الدفع"""
        self.paid_amount = paid_amount
        self.payment_method = payment_method
        self.calculate_totals()
    
    def clear_items(self):
        """مسح جميع العناصر"""
        self.items.clear()
        self.calculate_totals()
    
    def is_paid_in_full(self) -> bool:
        """التحقق من اكتمال الدفع"""
        return self.paid_amount >= self.total_amount
    
    def get_items_count(self) -> int:
        """عدد العناصر في الفاتورة"""
        return sum(item.quantity for item in self.items)
    
    def get_unique_items_count(self) -> int:
        """عدد المنتجات المختلفة"""
        return len(self.items)
    
    def to_dict(self) -> dict:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'receipt_number': self.receipt_number,
            'user_id': self.user_id,
            'cashier_name': self.cashier_name,
            'customer_name': self.customer_name,
            'customer_phone': self.customer_phone,
            'items': [item.to_dict() for item in self.items],
            'subtotal': self.subtotal,
            'discount_amount': self.discount_amount,
            'discount_percentage': self.discount_percentage,
            'tax_amount': self.tax_amount,
            'tax_rate': self.tax_rate,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'change_amount': self.change_amount,
            'payment_method': self.payment_method,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Sale':
        """إنشاء مبيعة من قاموس"""
        items = []
        for item_data in data.get('items', []):
            items.append(SaleItem(**item_data))
        
        return cls(
            id=data.get('id'),
            receipt_number=data.get('receipt_number', ''),
            user_id=data.get('user_id', 0),
            cashier_name=data.get('cashier_name', ''),
            customer_name=data.get('customer_name', ''),
            customer_phone=data.get('customer_phone', ''),
            items=items,
            subtotal=data.get('subtotal', 0.0),
            discount_amount=data.get('discount_amount', 0.0),
            discount_percentage=data.get('discount_percentage', 0.0),
            tax_amount=data.get('tax_amount', 0.0),
            tax_rate=data.get('tax_rate', 19.0),
            total_amount=data.get('total_amount', 0.0),
            paid_amount=data.get('paid_amount', 0.0),
            change_amount=data.get('change_amount', 0.0),
            payment_method=data.get('payment_method', 'نقدي'),
            status=data.get('status', 'مكتملة'),
            notes=data.get('notes', ''),
            created_at=data.get('created_at')
        )
    
    def __str__(self) -> str:
        return f"فاتورة {self.receipt_number} - {self.total_amount} دج"
    
    def __repr__(self) -> str:
        return f"Sale(receipt_number='{self.receipt_number}', total_amount={self.total_amount})"
