# -*- coding: utf-8 -*-
"""
نموذج المنتج
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class Product:
    """نموذج بيانات المنتج"""
    id: Optional[int] = None
    barcode: str = ""
    name: str = ""
    description: str = ""
    category: str = ""
    price: float = 0.0
    cost_price: float = 0.0
    stock_quantity: int = 0
    min_stock_level: int = 5
    max_stock_level: int = 100
    unit: str = "قطعة"
    image_path: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """التحقق من صحة البيانات بعد الإنشاء"""
        if not self.barcode:
            raise ValueError("الباركود مطلوب")
        if not self.name:
            raise ValueError("اسم المنتج مطلوب")
        if self.price < 0:
            raise ValueError("السعر يجب أن يكون أكبر من أو يساوي صفر")
        if self.stock_quantity < 0:
            raise ValueError("الكمية يجب أن تكون أكبر من أو تساوي صفر")
    
    @property
    def is_low_stock(self) -> bool:
        """التحقق من انخفاض المخزون"""
        return self.stock_quantity <= self.min_stock_level
    
    @property
    def profit_margin(self) -> float:
        """حساب هامش الربح"""
        if self.cost_price > 0:
            return ((self.price - self.cost_price) / self.cost_price) * 100
        return 0.0
    
    @property
    def profit_amount(self) -> float:
        """مبلغ الربح للوحدة الواحدة"""
        return self.price - self.cost_price
    
    @property
    def stock_value(self) -> float:
        """قيمة المخزون الحالي"""
        return self.stock_quantity * self.cost_price
    
    def to_dict(self) -> dict:
        """تحويل المنتج إلى قاموس"""
        return {
            'id': self.id,
            'barcode': self.barcode,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'price': self.price,
            'cost_price': self.cost_price,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'max_stock_level': self.max_stock_level,
            'unit': self.unit,
            'image_path': self.image_path,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Product':
        """إنشاء منتج من قاموس"""
        return cls(
            id=data.get('id'),
            barcode=data.get('barcode', ''),
            name=data.get('name', ''),
            description=data.get('description', ''),
            category=data.get('category', ''),
            price=float(data.get('price', 0)),
            cost_price=float(data.get('cost_price', 0)),
            stock_quantity=int(data.get('stock_quantity', 0)),
            min_stock_level=int(data.get('min_stock_level', 5)),
            max_stock_level=int(data.get('max_stock_level', 100)),
            unit=data.get('unit', 'قطعة'),
            image_path=data.get('image_path', ''),
            is_active=bool(data.get('is_active', True)),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
    
    def update_stock(self, quantity: int, operation: str = 'add') -> bool:
        """تحديث المخزون"""
        if operation == 'add':
            self.stock_quantity += quantity
        elif operation == 'subtract':
            if self.stock_quantity >= quantity:
                self.stock_quantity -= quantity
            else:
                return False
        elif operation == 'set':
            if quantity >= 0:
                self.stock_quantity = quantity
            else:
                return False
        
        self.updated_at = datetime.now()
        return True
    
    def calculate_total_price(self, quantity: int, discount_percentage: float = 0) -> float:
        """حساب السعر الإجمالي مع الخصم"""
        subtotal = self.price * quantity
        discount_amount = subtotal * (discount_percentage / 100)
        return subtotal - discount_amount
    
    def __str__(self) -> str:
        return f"{self.name} ({self.barcode}) - {self.price} دج"
    
    def __repr__(self) -> str:
        return f"Product(id={self.id}, name='{self.name}', barcode='{self.barcode}', price={self.price})"
