# -*- coding: utf-8 -*-
"""
نموذج المستخدم
"""

from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime
import bcrypt

@dataclass
class User:
    """نموذج بيانات المستخدم"""
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    full_name: str = ""
    role: str = "cashier"
    email: str = ""
    phone: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    login_attempts: int = 0
    
    def __post_init__(self):
        """التحقق من صحة البيانات"""
        if not self.username:
            raise ValueError("اسم المستخدم مطلوب")
        if not self.full_name:
            raise ValueError("الاسم الكامل مطلوب")
        if self.role not in ['admin', 'manager', 'cashier']:
            raise ValueError("دور المستخدم غير صحيح")
    
    def set_password(self, password: str):
        """تعيين كلمة مرور مشفرة"""
        if len(password) < 6:
            raise ValueError("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
        
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        if not self.password_hash:
            return False
        
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def has_permission(self, permission: str) -> bool:
        """التحقق من صلاحية المستخدم"""
        permissions = self.get_permissions()
        return 'all' in permissions or permission in permissions
    
    def get_permissions(self) -> List[str]:
        """الحصول على صلاحيات المستخدم"""
        role_permissions = {
            'admin': ['all'],
            'manager': ['sales', 'inventory', 'reports', 'users', 'settings'],
            'cashier': ['sales', 'view_products', 'print_receipt']
        }
        
        return role_permissions.get(self.role, [])
    
    def get_role_name(self) -> str:
        """الحصول على اسم الدور بالعربية"""
        role_names = {
            'admin': 'مدير النظام',
            'manager': 'مدير المتجر',
            'cashier': 'كاشير'
        }
        
        return role_names.get(self.role, 'غير محدد')
    
    def is_locked(self) -> bool:
        """التحقق من قفل الحساب"""
        return self.login_attempts >= 3
    
    def reset_login_attempts(self):
        """إعادة تعيين محاولات تسجيل الدخول"""
        self.login_attempts = 0
        self.last_login = datetime.now()
    
    def increment_login_attempts(self):
        """زيادة محاولات تسجيل الدخول الفاشلة"""
        self.login_attempts += 1
    
    def to_dict(self) -> dict:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'role': self.role,
            'email': self.email,
            'phone': self.phone,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'login_attempts': self.login_attempts
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'User':
        """إنشاء مستخدم من قاموس"""
        return cls(
            id=data.get('id'),
            username=data.get('username', ''),
            password_hash=data.get('password_hash', ''),
            full_name=data.get('full_name', ''),
            role=data.get('role', 'cashier'),
            email=data.get('email', ''),
            phone=data.get('phone', ''),
            is_active=bool(data.get('is_active', True)),
            created_at=data.get('created_at'),
            last_login=data.get('last_login'),
            login_attempts=int(data.get('login_attempts', 0))
        )
    
    def __str__(self) -> str:
        return f"{self.full_name} ({self.username}) - {self.get_role_name()}"
    
    def __repr__(self) -> str:
        return f"User(id={self.id}, username='{self.username}', role='{self.role}')"
