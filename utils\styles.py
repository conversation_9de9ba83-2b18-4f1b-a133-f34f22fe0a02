# -*- coding: utf-8 -*-
"""
أنماط التصميم النيومورفيك للواجهة العربية
"""

# الألوان الأساسية للتصميم النيومورفيك
COLORS = {
    'background': '#E0E5EC',
    'surface': '#E0E5EC',
    'primary': '#4A90E2',
    'secondary': '#7B68EE',
    'success': '#28A745',
    'warning': '#FFC107',
    'danger': '#DC3545',
    'info': '#17A2B8',
    'light': '#F8F9FA',
    'dark': '#343A40',
    'text_primary': '#2C3E50',
    'text_secondary': '#6C757D',
    'shadow_light': '#FFFFFF',
    'shadow_dark': '#A3B1C6',
    'border': '#D1D9E6'
}

# أنماط النيومورفيك الأساسية
NEUMORPHIC_STYLES = {
    'main_window': f"""
        QMainWindow {{
            background-color: {COLORS['background']};
            color: {COLORS['text_primary']};
            font-family: 'Arial', 'Tahoma', sans-serif;
            font-size: 12px;
        }}
    """,
    
    'button_primary': f"""
        QPushButton {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 15px;
            padding: 12px 24px;
            color: {COLORS['text_primary']};
            font-weight: bold;
            font-size: 14px;
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']}, 
                       -8px -8px 16px {COLORS['shadow_light']};
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary']};
            color: white;
            box-shadow: inset 4px 4px 8px {COLORS['shadow_dark']}, 
                       inset -4px -4px 8px {COLORS['shadow_light']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary']};
            box-shadow: inset 6px 6px 12px {COLORS['shadow_dark']}, 
                       inset -6px -6px 12px {COLORS['shadow_light']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['surface']};
            color: {COLORS['text_secondary']};
            box-shadow: none;
        }}
    """,
    
    'button_success': f"""
        QPushButton {{
            background-color: {COLORS['success']};
            border: none;
            border-radius: 15px;
            padding: 12px 24px;
            color: white;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']}, 
                       -8px -8px 16px {COLORS['shadow_light']};
        }}
        QPushButton:hover {{
            background-color: #218838;
            box-shadow: inset 4px 4px 8px rgba(0,0,0,0.2), 
                       inset -4px -4px 8px rgba(255,255,255,0.1);
        }}
        QPushButton:pressed {{
            box-shadow: inset 6px 6px 12px rgba(0,0,0,0.3), 
                       inset -6px -6px 12px rgba(255,255,255,0.1);
        }}
    """,
    
    'button_danger': f"""
        QPushButton {{
            background-color: {COLORS['danger']};
            border: none;
            border-radius: 15px;
            padding: 12px 24px;
            color: white;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']}, 
                       -8px -8px 16px {COLORS['shadow_light']};
        }}
        QPushButton:hover {{
            background-color: #C82333;
            box-shadow: inset 4px 4px 8px rgba(0,0,0,0.2), 
                       inset -4px -4px 8px rgba(255,255,255,0.1);
        }}
        QPushButton:pressed {{
            box-shadow: inset 6px 6px 12px rgba(0,0,0,0.3), 
                       inset -6px -6px 12px rgba(255,255,255,0.1);
        }}
    """,
    
    'input_field': f"""
        QLineEdit {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: {COLORS['text_primary']};
            font-size: 14px;
            box-shadow: inset 4px 4px 8px {COLORS['shadow_dark']}, 
                       inset -4px -4px 8px {COLORS['shadow_light']};
        }}
        QLineEdit:focus {{
            border: 2px solid {COLORS['primary']};
            box-shadow: inset 2px 2px 4px {COLORS['shadow_dark']}, 
                       inset -2px -2px 4px {COLORS['shadow_light']};
        }}
    """,
    
    'table_widget': f"""
        QTableWidget {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 15px;
            gridline-color: {COLORS['border']};
            color: {COLORS['text_primary']};
            font-size: 12px;
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']}, 
                       -8px -8px 16px {COLORS['shadow_light']};
        }}
        QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {COLORS['border']};
        }}
        QTableWidget::item:selected {{
            background-color: {COLORS['primary']};
            color: white;
        }}
        QHeaderView::section {{
            background-color: {COLORS['surface']};
            color: {COLORS['text_primary']};
            padding: 12px;
            border: none;
            border-bottom: 2px solid {COLORS['primary']};
            font-weight: bold;
        }}
    """,
    
    'card_widget': f"""
        QWidget {{
            background-color: {COLORS['surface']};
            border-radius: 20px;
            padding: 20px;
            box-shadow: 12px 12px 24px {COLORS['shadow_dark']}, 
                       -12px -12px 24px {COLORS['shadow_light']};
        }}
    """,
    
    'label_title': f"""
        QLabel {{
            color: {COLORS['text_primary']};
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
        }}
    """,
    
    'label_subtitle': f"""
        QLabel {{
            color: {COLORS['text_secondary']};
            font-size: 14px;
            padding: 5px;
        }}
    """,
    
    'combo_box': f"""
        QComboBox {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 12px;
            padding: 8px 12px;
            color: {COLORS['text_primary']};
            font-size: 14px;
            box-shadow: inset 4px 4px 8px {COLORS['shadow_dark']}, 
                       inset -4px -4px 8px {COLORS['shadow_light']};
        }}
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        QComboBox::down-arrow {{
            image: url(down_arrow.png);
            width: 12px;
            height: 12px;
        }}
        QComboBox QAbstractItemView {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 8px;
            selection-background-color: {COLORS['primary']};
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']};
        }}
    """,
    
    'scroll_area': f"""
        QScrollArea {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 15px;
        }}
        QScrollBar:vertical {{
            background-color: {COLORS['surface']};
            width: 12px;
            border-radius: 6px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {COLORS['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {COLORS['secondary']};
        }}
    """,
    
    'menu_bar': f"""
        QMenuBar {{
            background-color: {COLORS['surface']};
            color: {COLORS['text_primary']};
            border: none;
            padding: 5px;
            box-shadow: 0px 4px 8px {COLORS['shadow_dark']};
        }}
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 8px;
        }}
        QMenuBar::item:selected {{
            background-color: {COLORS['primary']};
            color: white;
        }}
        QMenu {{
            background-color: {COLORS['surface']};
            border: none;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 8px 8px 16px {COLORS['shadow_dark']};
        }}
        QMenu::item {{
            padding: 8px 16px;
            border-radius: 6px;
        }}
        QMenu::item:selected {{
            background-color: {COLORS['primary']};
            color: white;
        }}
    """,
    
    'status_bar': f"""
        QStatusBar {{
            background-color: {COLORS['surface']};
            color: {COLORS['text_primary']};
            border: none;
            padding: 5px;
            box-shadow: 0px -2px 4px {COLORS['shadow_dark']};
        }}
    """
}

def get_style(style_name):
    """الحصول على نمط معين"""
    return NEUMORPHIC_STYLES.get(style_name, "")

def get_combined_styles(*style_names):
    """دمج عدة أنماط معاً"""
    combined = ""
    for style_name in style_names:
        combined += get_style(style_name) + "\n"
    return combined

def apply_rtl_layout(widget):
    """تطبيق تخطيط من اليمين إلى اليسار"""
    from PyQt5.QtCore import Qt
    widget.setLayoutDirection(Qt.RightToLeft)

def set_arabic_font(widget, size=12, bold=False):
    """تعيين خط عربي للعنصر"""
    from PyQt5.QtGui import QFont
    font = QFont("Arial", size)
    font.setBold(bold)
    widget.setFont(font)
