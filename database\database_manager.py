# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات لنظام نقطة البيع
"""

import sqlite3
import os
import hashlib
import bcrypt
from datetime import datetime, timedelta
from pathlib import Path
import json
import shutil

class DatabaseManager:
    def __init__(self, db_path="data/pos_database.db"):
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
    
    def ensure_database_directory(self):
        """التأكد من وجود مجلد قاعدة البيانات"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'cashier',
                email TEXT,
                phone TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                login_attempts INTEGER DEFAULT 0
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                price REAL NOT NULL,
                cost_price REAL DEFAULT 0,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 5,
                max_stock_level INTEGER DEFAULT 100,
                unit TEXT DEFAULT 'قطعة',
                image_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number TEXT UNIQUE NOT NULL,
                user_id INTEGER NOT NULL,
                customer_name TEXT,
                customer_phone TEXT,
                subtotal REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                discount_percentage REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                paid_amount REAL NOT NULL,
                change_amount REAL DEFAULT 0,
                payment_method TEXT DEFAULT 'نقدي',
                status TEXT DEFAULT 'مكتملة',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول تفاصيل المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول حركة المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                quantity INTEGER NOT NULL,
                reference_type TEXT, -- 'sale', 'purchase', 'adjustment'
                reference_id INTEGER,
                notes TEXT,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT UNIQUE,
                email TEXT,
                address TEXT,
                loyalty_points INTEGER DEFAULT 0,
                total_purchases REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول سجل العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT NOT NULL,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول النسخ الاحتياطية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                backup_type TEXT DEFAULT 'manual',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إنشاء المستخدم الافتراضي
        self.create_default_admin()
        self.create_default_settings()
    
    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود مستخدم مدير
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # إنشاء كلمة مرور مشفرة
            password = "admin123"
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", password_hash, "المدير العام", "admin", "<EMAIL>"))
            
            conn.commit()
        
        conn.close()
    
    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        default_settings = [
            ("store_name", "متجر الكاشير", "اسم المتجر"),
            ("store_address", "الجزائر العاصمة، الجزائر", "عنوان المتجر"),
            ("store_phone", "+*********** 789", "هاتف المتجر"),
            ("store_email", "<EMAIL>", "بريد المتجر الإلكتروني"),
            ("tax_rate", "19", "معدل الضريبة (%)"),
            ("currency", "دج", "العملة"),
            ("receipt_footer", "شكراً لزيارتكم", "تذييل الفاتورة"),
            ("auto_backup", "1", "النسخ الاحتياطي التلقائي"),
            ("backup_time", "02:00", "وقت النسخ الاحتياطي"),
            ("low_stock_alert", "1", "تنبيه نفاد المخزون"),
            ("print_receipt", "1", "طباعة الفاتورة تلقائياً")
        ]
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        for key, value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO settings (key, value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))
        
        conn.commit()
        conn.close()
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, password_hash, full_name, role, is_active, login_attempts
            FROM users WHERE username = ?
        ''', (username,))
        
        user = cursor.fetchone()
        
        if not user:
            conn.close()
            return None
        
        # التحقق من حالة المستخدم
        if not user['is_active']:
            conn.close()
            return None
        
        # التحقق من محاولات تسجيل الدخول
        if user['login_attempts'] >= 3:
            conn.close()
            return None
        
        # التحقق من كلمة المرور
        if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            # تحديث آخر تسجيل دخول وإعادة تعيين المحاولات
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP, login_attempts = 0
                WHERE id = ?
            ''', (user['id'],))
            conn.commit()
            conn.close()
            return dict(user)
        else:
            # زيادة عدد المحاولات الفاشلة
            cursor.execute('''
                UPDATE users SET login_attempts = login_attempts + 1
                WHERE id = ?
            ''', (user['id'],))
            conn.commit()
            conn.close()
            return None
    
    def add_product(self, product_data):
        """إضافة منتج جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO products (barcode, name, description, category, price, 
                                    cost_price, stock_quantity, min_stock_level, 
                                    max_stock_level, unit, image_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_data['barcode'],
                product_data['name'],
                product_data.get('description', ''),
                product_data.get('category', ''),
                product_data['price'],
                product_data.get('cost_price', 0),
                product_data.get('stock_quantity', 0),
                product_data.get('min_stock_level', 5),
                product_data.get('max_stock_level', 100),
                product_data.get('unit', 'قطعة'),
                product_data.get('image_path', '')
            ))
            
            product_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return product_id
        except sqlite3.IntegrityError:
            conn.close()
            return None
    
    def get_product_by_barcode(self, barcode):
        """البحث عن منتج بالباركود"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM products WHERE barcode = ? AND is_active = 1
        ''', (barcode,))
        
        product = cursor.fetchone()
        conn.close()
        
        return dict(product) if product else None
    
    def get_all_products(self, active_only=True):
        """الحصول على جميع المنتجات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM products"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        cursor.execute(query)
        products = cursor.fetchall()
        conn.close()
        
        return [dict(product) for product in products]
    
    def update_product_stock(self, product_id, quantity, movement_type, reference_type=None, reference_id=None, user_id=None):
        """تحديث مخزون المنتج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # تحديث المخزون
            if movement_type == 'out':
                cursor.execute('''
                    UPDATE products SET stock_quantity = stock_quantity - ?, 
                                      updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (quantity, product_id))
            else:
                cursor.execute('''
                    UPDATE products SET stock_quantity = stock_quantity + ?, 
                                      updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (quantity, product_id))
            
            # تسجيل حركة المخزون
            cursor.execute('''
                INSERT INTO inventory_movements (product_id, movement_type, quantity, 
                                               reference_type, reference_id, user_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (product_id, movement_type, quantity, reference_type, reference_id, user_id))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            conn.close()
            return False
    
    def create_sale(self, sale_data, sale_items, user_id):
        """إنشاء عملية بيع جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # إنشاء رقم فاتورة فريد
            receipt_number = self.generate_receipt_number()
            
            # إدراج بيانات البيع
            cursor.execute('''
                INSERT INTO sales (receipt_number, user_id, customer_name, customer_phone,
                                 subtotal, discount_amount, discount_percentage, tax_amount,
                                 total_amount, paid_amount, change_amount, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                receipt_number,
                user_id,
                sale_data.get('customer_name', ''),
                sale_data.get('customer_phone', ''),
                sale_data['subtotal'],
                sale_data.get('discount_amount', 0),
                sale_data.get('discount_percentage', 0),
                sale_data.get('tax_amount', 0),
                sale_data['total_amount'],
                sale_data['paid_amount'],
                sale_data.get('change_amount', 0),
                sale_data.get('payment_method', 'نقدي'),
                sale_data.get('notes', '')
            ))
            
            sale_id = cursor.lastrowid
            
            # إدراج تفاصيل البيع وتحديث المخزون
            for item in sale_items:
                cursor.execute('''
                    INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, 
                                          total_price, discount_amount)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    item.get('discount_amount', 0)
                ))
                
                # تحديث المخزون
                self.update_product_stock(
                    item['product_id'], 
                    item['quantity'], 
                    'out', 
                    'sale', 
                    sale_id, 
                    user_id
                )
            
            conn.commit()
            conn.close()
            return sale_id, receipt_number
        except Exception as e:
            conn.rollback()
            conn.close()
            return None, None
    
    def generate_receipt_number(self):
        """توليد رقم فاتورة فريد"""
        from datetime import datetime
        now = datetime.now()
        timestamp = now.strftime("%Y%m%d%H%M%S")
        return f"REC{timestamp}"
    
    def get_sales_report(self, start_date, end_date):
        """تقرير المبيعات لفترة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.*, u.full_name as cashier_name
            FROM sales s
            LEFT JOIN users u ON s.user_id = u.id
            WHERE DATE(s.created_at) BETWEEN ? AND ?
            ORDER BY s.created_at DESC
        ''', (start_date, end_date))
        
        sales = cursor.fetchall()
        conn.close()
        
        return [dict(sale) for sale in sales]
    
    def get_low_stock_products(self):
        """المنتجات التي تحتاج إعادة تموين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM products 
            WHERE stock_quantity <= min_stock_level AND is_active = 1
            ORDER BY stock_quantity ASC
        ''')
        
        products = cursor.fetchall()
        conn.close()
        
        return [dict(product) for product in products]
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup/pos_backup_{timestamp}.db"
        
        try:
            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = Path(backup_path).parent
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # تسجيل النسخة الاحتياطية
            conn = self.get_connection()
            cursor = conn.cursor()
            
            file_size = os.path.getsize(backup_path)
            cursor.execute('''
                INSERT INTO backups (filename, file_path, file_size, backup_type)
                VALUES (?, ?, ?, ?)
            ''', (os.path.basename(backup_path), backup_path, file_size, 'manual'))
            
            conn.commit()
            conn.close()
            
            return True
        except Exception as e:
            return False
    
    def get_setting(self, key, default_value=None):
        """الحصول على إعداد معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
        result = cursor.fetchone()
        conn.close()
        
        return result['value'] if result else default_value
    
    def update_setting(self, key, value):
        """تحديث إعداد معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (key, value))
        
        conn.commit()
        conn.close()
        return True
