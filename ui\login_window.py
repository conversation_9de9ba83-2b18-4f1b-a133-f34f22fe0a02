# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QApplication, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
from database.database_manager import DatabaseManager
from utils.styles import NEUMORPHIC_STYLES, apply_rtl_layout, set_arabic_font

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.failed_attempts = 0
        self.max_attempts = 3
        self.lockout_timer = QTimer()
        self.lockout_timer.timeout.connect(self.unlock_login)
        
        self.init_ui()
        self.setup_styles()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام نقطة البيع")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.FramelessWindowHint)
        
        # تطبيق تخطيط RTL
        apply_rtl_layout(self)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(20)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("login_frame")
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(25)
        
        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setText("🏪")
        logo_label.setStyleSheet("font-size: 48px; margin-bottom: 10px;")
        login_layout.addWidget(logo_label)
        
        # عنوان التطبيق
        title_label = QLabel("نظام نقطة البيع")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("title_label")
        set_arabic_font(title_label, 20, True)
        login_layout.addWidget(title_label)
        
        # عنوان فرعي
        subtitle_label = QLabel("تسجيل الدخول إلى النظام")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitle_label")
        set_arabic_font(subtitle_label, 14)
        login_layout.addWidget(subtitle_label)
        
        # حقل اسم المستخدم
        username_layout = QVBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("field_label")
        set_arabic_font(username_label, 12, True)
        username_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("input_field")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.returnPressed.connect(self.login)
        set_arabic_font(self.username_input, 12)
        username_layout.addWidget(self.username_input)
        
        login_layout.addLayout(username_layout)
        
        # حقل كلمة المرور
        password_layout = QVBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("field_label")
        set_arabic_font(password_label, 12, True)
        password_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setObjectName("input_field")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.returnPressed.connect(self.login)
        set_arabic_font(self.password_input, 12)
        password_layout.addWidget(self.password_input)
        
        login_layout.addLayout(password_layout)
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكر بياناتي")
        self.remember_checkbox.setObjectName("checkbox")
        set_arabic_font(self.remember_checkbox, 11)
        login_layout.addWidget(self.remember_checkbox)
        
        # رسالة الخطأ
        self.error_label = QLabel()
        self.error_label.setObjectName("error_label")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setWordWrap(True)
        self.error_label.hide()
        set_arabic_font(self.error_label, 11)
        login_layout.addWidget(self.error_label)
        
        # أزرار العمليات
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("login_button")
        self.login_button.clicked.connect(self.login)
        set_arabic_font(self.login_button, 14, True)
        buttons_layout.addWidget(self.login_button)
        
        # زر الخروج
        exit_button = QPushButton("خروج")
        exit_button.setObjectName("exit_button")
        exit_button.clicked.connect(self.close)
        set_arabic_font(exit_button, 12)
        buttons_layout.addWidget(exit_button)
        
        login_layout.addLayout(buttons_layout)
        
        # معلومات النسخة
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setObjectName("version_label")
        set_arabic_font(version_label, 10)
        login_layout.addWidget(version_label)
        
        main_layout.addWidget(login_frame)
        self.setLayout(main_layout)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def setup_styles(self):
        """إعداد الأنماط"""
        style = f"""
            QWidget {{
                background-color: #E0E5EC;
                color: #2C3E50;
                font-family: 'Arial', 'Tahoma', sans-serif;
            }}
            
            #login_frame {{
                background-color: #E0E5EC;
                border-radius: 25px;
                padding: 30px;
                box-shadow: 15px 15px 30px #A3B1C6, 
                           -15px -15px 30px #FFFFFF;
            }}
            
            #title_label {{
                color: #2C3E50;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 5px;
            }}
            
            #subtitle_label {{
                color: #6C757D;
                font-size: 14px;
                margin-bottom: 20px;
            }}
            
            #field_label {{
                color: #2C3E50;
                font-weight: bold;
                margin-bottom: 5px;
            }}
            
            #input_field {{
                background-color: #E0E5EC;
                border: none;
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 14px;
                color: #2C3E50;
                box-shadow: inset 6px 6px 12px #A3B1C6, 
                           inset -6px -6px 12px #FFFFFF;
            }}
            
            #input_field:focus {{
                border: 2px solid #4A90E2;
                box-shadow: inset 4px 4px 8px #A3B1C6, 
                           inset -4px -4px 8px #FFFFFF;
            }}
            
            #checkbox {{
                color: #2C3E50;
                spacing: 10px;
            }}
            
            #checkbox::indicator {{
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background-color: #E0E5EC;
                box-shadow: inset 3px 3px 6px #A3B1C6, 
                           inset -3px -3px 6px #FFFFFF;
            }}
            
            #checkbox::indicator:checked {{
                background-color: #4A90E2;
                box-shadow: inset 2px 2px 4px rgba(0,0,0,0.2);
            }}
            
            #login_button {{
                background-color: #4A90E2;
                border: none;
                border-radius: 15px;
                padding: 15px 30px;
                color: white;
                font-weight: bold;
                font-size: 16px;
                box-shadow: 8px 8px 16px #A3B1C6, 
                           -8px -8px 16px #FFFFFF;
            }}
            
            #login_button:hover {{
                background-color: #357ABD;
                box-shadow: inset 4px 4px 8px rgba(0,0,0,0.2), 
                           inset -4px -4px 8px rgba(255,255,255,0.1);
            }}
            
            #login_button:pressed {{
                box-shadow: inset 6px 6px 12px rgba(0,0,0,0.3), 
                           inset -6px -6px 12px rgba(255,255,255,0.1);
            }}
            
            #login_button:disabled {{
                background-color: #BDC3C7;
                color: #7F8C8D;
                box-shadow: none;
            }}
            
            #exit_button {{
                background-color: #E0E5EC;
                border: none;
                border-radius: 12px;
                padding: 12px 24px;
                color: #6C757D;
                font-weight: bold;
                box-shadow: 6px 6px 12px #A3B1C6, 
                           -6px -6px 12px #FFFFFF;
            }}
            
            #exit_button:hover {{
                background-color: #DC3545;
                color: white;
                box-shadow: inset 3px 3px 6px rgba(0,0,0,0.2), 
                           inset -3px -3px 6px rgba(255,255,255,0.1);
            }}
            
            #error_label {{
                color: #DC3545;
                background-color: rgba(220, 53, 69, 0.1);
                border: 1px solid rgba(220, 53, 69, 0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
            }}
            
            #version_label {{
                color: #95A5A6;
                font-size: 10px;
                margin-top: 20px;
            }}
        """
        
        self.setStyleSheet(style)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من الحقول
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # التحقق من القفل
        if self.failed_attempts >= self.max_attempts:
            self.show_error("تم قفل النظام مؤقتاً. يرجى المحاولة بعد 5 دقائق")
            return
        
        # تعطيل زر تسجيل الدخول مؤقتاً
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        # التحقق من بيانات المستخدم
        user = self.db_manager.authenticate_user(username, password)
        
        if user:
            # نجح تسجيل الدخول
            self.failed_attempts = 0
            self.hide_error()
            
            # حفظ بيانات المستخدم إذا كان مطلوباً
            if self.remember_checkbox.isChecked():
                self.save_user_credentials(username)
            
            # إرسال إشارة نجاح تسجيل الدخول
            self.login_successful.emit(user)
            self.close()
        else:
            # فشل تسجيل الدخول
            self.failed_attempts += 1
            remaining_attempts = self.max_attempts - self.failed_attempts
            
            if remaining_attempts > 0:
                self.show_error(f"اسم المستخدم أو كلمة المرور غير صحيحة. "
                              f"المحاولات المتبقية: {remaining_attempts}")
            else:
                self.show_error("تم تجاوز الحد الأقصى للمحاولات. سيتم قفل النظام لمدة 5 دقائق")
                self.lock_login()
            
            # مسح كلمة المرور
            self.password_input.clear()
            self.password_input.setFocus()
        
        # إعادة تفعيل زر تسجيل الدخول
        self.login_button.setEnabled(True)
        self.login_button.setText("تسجيل الدخول")
    
    def lock_login(self):
        """قفل تسجيل الدخول لمدة 5 دقائق"""
        self.login_button.setEnabled(False)
        self.username_input.setEnabled(False)
        self.password_input.setEnabled(False)
        
        # بدء مؤقت القفل (5 دقائق = 300000 مللي ثانية)
        self.lockout_timer.start(300000)
    
    def unlock_login(self):
        """إلغاء قفل تسجيل الدخول"""
        self.lockout_timer.stop()
        self.failed_attempts = 0
        
        self.login_button.setEnabled(True)
        self.username_input.setEnabled(True)
        self.password_input.setEnabled(True)
        
        self.hide_error()
        self.username_input.setFocus()
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        self.error_label.setText(message)
        self.error_label.show()
    
    def hide_error(self):
        """إخفاء رسالة الخطأ"""
        self.error_label.hide()
    
    def save_user_credentials(self, username):
        """حفظ بيانات المستخدم (اختياري)"""
        # يمكن تنفيذ حفظ اسم المستخدم في ملف إعدادات
        pass
    
    def load_saved_credentials(self):
        """تحميل بيانات المستخدم المحفوظة"""
        # يمكن تنفيذ تحميل اسم المستخدم من ملف إعدادات
        pass
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def showEvent(self, event):
        """عند عرض النافذة"""
        super().showEvent(event)
        self.center_on_screen()
        self.load_saved_credentials()
    
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.close()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.login()
        else:
            super().keyPressEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تعيين خط عربي للتطبيق
    font = QFont("Arial", 12)
    app.setFont(font)
    
    # تعيين اتجاه RTL
    app.setLayoutDirection(Qt.RightToLeft)
    
    login_window = LoginWindow()
    login_window.show()
    
    sys.exit(app.exec_())
