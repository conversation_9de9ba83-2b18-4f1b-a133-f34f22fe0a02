# -*- coding: utf-8 -*-
"""
إعدادات التطبيق الرئيسية
"""

import os
from pathlib import Path

# مسارات التطبيق
BASE_DIR = Path(__file__).parent.parent
DATABASE_PATH = BASE_DIR / "data" / "pos_database.db"
RESOURCES_PATH = BASE_DIR / "resources"
REPORTS_PATH = BASE_DIR / "reports"
BACKUP_PATH = BASE_DIR / "backup"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'type': 'sqlite',
    'path': str(DATABASE_PATH),
    'backup_interval': 24  # ساعة
}

# إعدادات المتجر
STORE_CONFIG = {
    'name': 'متجر الكاشير',
    'address': 'الجزائر العاصمة، الجزائر',
    'phone': '+*********** 789',
    'email': '<EMAIL>',
    'tax_id': '*********',
    'commercial_register': 'RC123456',
    'currency': 'دج',  # الدينار الجزائري
    'currency_symbol': 'دج',
    'logo_path': str(RESOURCES_PATH / "logo.png")
}

# إعدادات الطباعة
PRINTER_CONFIG = {
    'thermal_printer': True,
    'paper_width': 58,  # مم
    'font_size': 12,
    'auto_print': True,
    'copies': 1
}

# إعدادات الواجهة
UI_CONFIG = {
    'language': 'ar',
    'rtl': True,
    'theme': 'neumorphic',
    'font_family': 'Arial',
    'font_size': 12,
    'window_size': (1200, 800)
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout': 3600,  # ثانية
    'password_min_length': 6,
    'max_login_attempts': 3,
    'audit_log': True
}

# أدوار المستخدمين
USER_ROLES = {
    'admin': {
        'name': 'مدير',
        'permissions': ['all']
    },
    'cashier': {
        'name': 'كاشير',
        'permissions': ['sales', 'view_products', 'print_receipt']
    },
    'manager': {
        'name': 'مدير المتجر',
        'permissions': ['sales', 'inventory', 'reports', 'users']
    }
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_time': '02:00',  # 2:00 صباحاً
    'keep_backups': 30,  # عدد الأيام
    'cloud_backup': False,
    'cloud_provider': None  # 'google_drive', 'dropbox'
}

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    directories = [
        DATABASE_PATH.parent,
        RESOURCES_PATH,
        REPORTS_PATH,
        BACKUP_PATH
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
